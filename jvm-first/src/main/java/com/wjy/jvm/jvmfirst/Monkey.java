/**
 * Copyright (C), 2015-2022, 上海象翌微链有限公司
 * FileName: Monkey
 * Author:   Administrator
 * Date:     2022/10/28 9:03
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.wjy.jvm.jvmfirst;

/**
 * 〈〉
 *
 * <AUTHOR>
 * @create 2022/10/28
 * @since 1.0.0
 */
public class Monkey extends Animal{

    @Override
    public void walk() {
        System.out.println("走");
    }
}
