/**
 * Copyright (C), 2015-2023, 上海象翌微链有限公司
 * FileName: Tst
 * Author:   Administrator
 * Date:     2023/7/2 9:46
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.wjy.jvm.jvmfirst;

/**
 * 〈〉
 *
 * <AUTHOR>
 * @create 2023/7/2
 * @since 1.0.0
 */
public class Tst {

    public static void main(String[] args) {
        String lz_sql = "select t.sid as ssid from TASK_LCLZTZB a left join sa_task t on a.fprocess=t.sprocess and a.flzhj=t.sactivityintemplate"
                + " where t.sStatusID in('tesReady','tesExecuting') and t.sexecutorpersonname is not null AND t.sexecutemode in ('temSimultaneous','temSequential')"
                + " AND EXISTS(select m.sID,m.sCreateTime,count(x.fRQ) as fTS  from sa_task m "
                + " left join TASK_LCLZTZB c on c.fprocess=m.sprocess and c.ftzhj=m.sactivityintemplate left join HR_KQRLXXB x on  "
                + " to_char(x.fRQ,'yyyy-MM-dd')>to_char(m.sCreateTime,'yyyy-MM-dd')  and to_char(x.fRQ,'yyyy-MM-dd')<=to_char(sysdate,'yyyy-MM-dd')"
                + " where t.sid=m.sid group by m.sID,m.sCreateTime having  round((sysdate- m.sCreateTime)*24,0)-sum(case when x.fLX='休' then 1 else 0 end)*24>=a.flzsj) and a.scount<=3"
                + " union"
                + " select ssid from (select t.sdata1,t.sname,t.sactivityintemplate,t.sid as ssid,ROW_NUMBER() OVER(PARTITION BY t.sdata1,t.sactivityintemplate ORDER BY  t.sdata1,t.sactivityintemplate  asc) RN from TASK_LCLZTZB a "
                + " left join sa_task t on a.fprocess=t.sprocess and a.flzhj=t.sactivityintemplate "
                + " where t.sStatusID in('tesReady','tesExecuting') and t.sexecutorpersonname is not null and t.sexecutemode='temPreempt' "
                + " AND EXISTS(select m.sID,m.sCreateTime,count(x.fRQ) as fTS  from sa_task m "
                + " left join TASK_LCLZTZB c on c.fprocess=m.sprocess and c.ftzhj=m.sactivityintemplate left join HR_KQRLXXB x on "
                + " to_char(x.fRQ,'yyyy-MM-dd')>to_char(m.sCreateTime,'yyyy-MM-dd')  and to_char(x.fRQ,'yyyy-MM-dd')<=to_char(sysdate,'yyyy-MM-dd')"
                + " where t.sid=m.sid group by m.sID,m.sCreateTime having  round((sysdate- m.sCreateTime)*24,0)-sum(case when x.fLX='休' then 1 else 0 end)*24>=a.flzsj) and a.scount<=3 )where RN=1";
        System.out.println(lz_sql);
    }
}
