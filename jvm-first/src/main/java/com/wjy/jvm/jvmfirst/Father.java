/**
 * Copyright (C), 2015-2022, 上海象翌微链有限公司
 * FileName: Father
 * Author:   Administrator
 * Date:     2022/10/28 8:22
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.wjy.jvm.jvmfirst;

/**
 * 〈〉
 *
 * <AUTHOR>
 * @create 2022/10/28
 * @since 1.0.0
 */
public class Father {
    public static String hello = "00";

    public static boolean hasChildren() {
        return true;
    }

    public void test(Animal a) {
        a.walk();
    }

    public static void main(String[] args) {
        hasChildren();
        Father f = new Father();

    }
}
